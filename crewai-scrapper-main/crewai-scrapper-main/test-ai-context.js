// Test script to verify AI context building
import { generateSearchTermFromConversation } from './src/third-party/OpenAI/aiChatService.js';

// Mock conversation context (simulating what would be stored)
const mockConversationContext = {
  searchHistory: ['pants'],
  sessionMetadata: {
    categories: ['pants'],
    attributes: []
  },
  productContext: {
    categories: ['pants'],
    colors: [],
    styles: [],
    sizes: []
  }
};

// Mock messages (simulating previous conversation)
const mockMessages = [
  {
    role: 'user',
    content: 'pants',
    searchTermGenerated: 'pants'
  },
  {
    role: 'assistant',
    content: "I'll search for pants for you.",
    searchTermGenerated: 'pants'
  }
];

// Test scenarios
const testScenarios = [
  {
    name: 'Context Building - Green + Pants',
    messages: mockMessages,
    userMessage: 'green',
    context: mockConversationContext,
    expected: 'green pants'
  },
  {
    name: 'Context Building - Large + Pants',
    messages: mockMessages,
    userMessage: 'large',
    context: mockConversationContext,
    expected: 'large pants'
  },
  {
    name: 'Context Building - Blue Skinny + Pants',
    messages: mockMessages,
    userMessage: 'blue skinny',
    context: mockConversationContext,
    expected: 'blue skinny pants'
  },
  {
    name: 'New Category - Shoes',
    messages: mockMessages,
    userMessage: 'shoes',
    context: mockConversationContext,
    expected: 'shoes'
  }
];

// Run tests
async function runTests() {
  console.log('🧪 Testing AI Context Building...\n');
  
  for (const scenario of testScenarios) {
    console.log(`Testing: ${scenario.name}`);
    console.log(`Input: "${scenario.userMessage}"`);
    console.log(`Context: categories = [${scenario.context.sessionMetadata.categories.join(', ')}]`);
    
    try {
      // Note: This will fail without OpenAI API key, but we can test the fallback logic
      const result = await generateSearchTermFromConversation(
        scenario.messages, 
        scenario.userMessage, 
        scenario.context
      );
      
      console.log(`✅ Generated: "${result.searchTerm}"`);
      console.log(`Expected: "${scenario.expected}"`);
      console.log(`Match: ${result.searchTerm === scenario.expected ? '✅' : '❌'}`);
      
    } catch (error) {
      console.log(`⚠️  AI API failed (expected without API key), testing fallback...`);
      
      // Test fallback logic directly
      const { generateFallbackSearchTerm } = await import('./src/third-party/OpenAI/aiChatService.js');
      const fallbackResult = generateFallbackSearchTerm(
        scenario.messages, 
        scenario.userMessage, 
        scenario.context
      );
      
      console.log(`🔄 Fallback Generated: "${fallbackResult}"`);
      console.log(`Expected: "${scenario.expected}"`);
      console.log(`Match: ${fallbackResult === scenario.expected ? '✅' : '❌'}`);
    }
    
    console.log('---\n');
  }
}

// Run the tests
runTests().catch(console.error);
